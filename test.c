#include <stdio.h>
#include <assert.h>
#include <string.h>

// 简单的数学函数
int add(int a, int b) {
    return a + b;
}

int multiply(int a, int b) {
    return a * b;
}

// 字符串处理函数
int string_length(const char* str) {
    if (str == NULL) return -1;
    return strlen(str);
}

// 判断是否为偶数
int is_even(int num) {
    return num % 2 == 0;
}

// 测试函数
void test_add() {
    printf("测试 add 函数...\n");
    assert(add(2, 3) == 5);
    assert(add(-1, 1) == 0);
    assert(add(0, 0) == 0);
    printf("✓ add 函数测试通过\n");
}

void test_multiply() {
    printf("测试 multiply 函数...\n");
    assert(multiply(3, 4) == 12);
    assert(multiply(-2, 5) == -10);
    assert(multiply(0, 100) == 0);
    printf("✓ multiply 函数测试通过\n");
}

void test_string_length() {
    printf("测试 string_length 函数...\n");
    assert(string_length("hello") == 5);
    assert(string_length("") == 0);
    assert(string_length("test") == 4);
    assert(string_length(NULL) == -1);
    printf("✓ string_length 函数测试通过\n");
}

void test_is_even() {
    printf("测试 is_even 函数...\n");
    assert(is_even(2) == 1);
    assert(is_even(3) == 0);
    assert(is_even(0) == 1);
    assert(is_even(-4) == 1);
    assert(is_even(-3) == 0);
    printf("✓ is_even 函数测试通过\n");
}

// 运行所有测试
void run_all_tests() {
    printf("开始运行测试...\n\n");

    test_add();
    test_multiply();
    test_string_length();
    test_is_even();

    printf("\n🎉 所有测试都通过了！\n");
}

int main() {
    printf("=== 简单的 C 语言测试程序 ===\n\n");

    // 运行测试
    run_all_tests();

    // 演示功能
    printf("\n=== 功能演示 ===\n");
    printf("add(10, 20) = %d\n", add(10, 20));
    printf("multiply(6, 7) = %d\n", multiply(6, 7));
    printf("string_length(\"Hello World\") = %d\n", string_length("Hello World"));
    printf("is_even(42) = %s\n", is_even(42) ? "true" : "false");
    printf("is_even(13) = %s\n", is_even(13) ? "true" : "false");

    return 0;
}